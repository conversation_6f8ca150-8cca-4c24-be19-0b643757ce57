name: SonarQube Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true

jobs:
  sonarqube:
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Yarn cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.yarn/cache
            .yarn/cache
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'yarn' # Enable yarn caching

      - name: Enable Yarn and set version
        run: |
          corepack enable
          corepack prepare yarn@4.1.0 --activate
          yarn --version

      - name: Install dependencies
        run: yarn install --frozen-lockfile --network-timeout 300000


      - name: Run Tests (with coverage)
        run: yarn run test:coverage
      
      - name: Install SonarScanner
        run: yarn global add sonarqube-scanner
      
      - name: Configure Node.js Memory for SonarQube
        run: |
          # Set Node.js memory limits for SonarQube analysis
          export NODE_OPTIONS="--max-old-space-size=4096"
          echo "NODE_OPTIONS=--max-old-space-size=4096" >> $GITHUB_ENV
      
      - name: Run SonarQube Analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          NODE_OPTIONS: --max-old-space-size=4096
        run: |
          sonar-scanner \
            -Dsonar.projectKey=ats-frontend-application \
            -Dsonar.projectName="ATS Frontend Application" \
            -Dsonar.sources=src \
            -Dsonar.tests=test \
            -Dsonar.host.url=http://*************:9000/ \
            -Dsonar.login=$SONAR_TOKEN \
            -Dsonar.coverage.exclusions=src/components/ui/**,src/config.js,src/index.jsx,src/setupTests.js \
            -Dsonar.test.inclusions=test/unit/**/*.test.js,test/unit/**/*.test.jsx,test/integration/**/*.test.js,test/integration/**/*.test.jsx \
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
            -Dsonar.sourceEncoding=UTF-8 \
            -Dsonar.javascript.node.maxspace=4096 \
            -Dsonar.javascript.node.debugMemory=true
      
      - name: Wait for SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: http://*************:9000/ 