name: Build Health Monitor

on:
  workflow_call:
    inputs:
      timeout_minutes:
        description: 'Timeout for the build process'
        required: false
        default: 30
        type: number

jobs:
  health-check:
    runs-on: [self-hosted, Linux]
    timeout-minutes: ${{ inputs.timeout_minutes }}
    
    steps:
      - name: Pre-build System Check
        run: |
          echo "🔍 Checking system resources before build..."
          
          # Check available memory
          echo "Memory usage:"
          free -h
          
          # Check disk space
          echo "Disk usage:"
          df -h
          
          # Check CPU load
          echo "CPU load:"
          uptime
          
          # Check Docker status
          echo "Docker status:"
          docker system df || echo "Docker not available"
          
          # Set memory thresholds
          AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
          echo "Available memory: ${AVAILABLE_MEM}MB"
          
          if [ "$AVAILABLE_MEM" -lt 2048 ]; then
            echo "⚠️ Warning: Low memory available (${AVAILABLE_MEM}MB). Build may fail."
            echo "Attempting to free memory..."
            
            # Clean up Docker if available
            docker system prune -f || true
            
            # Clear npm cache
            npm cache clean --force || true
            
            # Clear yarn cache if exists
            yarn cache clean || true
            
            echo "Memory after cleanup:"
            free -h
          fi

      - name: Monitor Build Process
        run: |
          echo "🔄 Starting build monitoring..."
          
          # Create a background process to monitor system resources
          (
            while true; do
              echo "$(date): Memory: $(free -m | awk 'NR==2{printf "%.0f", $3}')MB used, Load: $(uptime | awk -F'load average:' '{print $2}')"
              sleep 30
            done
          ) &
          
          MONITOR_PID=$!
          echo "MONITOR_PID=$MONITOR_PID" >> $GITHUB_ENV
          
          echo "Build monitoring started with PID: $MONITOR_PID"

      - name: Post-build Cleanup
        if: always()
        run: |
          echo "🧹 Performing post-build cleanup..."
          
          # Kill monitoring process if it exists
          if [ ! -z "$MONITOR_PID" ]; then
            kill $MONITOR_PID 2>/dev/null || true
          fi
          
          # Clean up build artifacts and caches
          rm -rf node_modules/.cache || true
          rm -rf dist/.vite || true
          
          # Docker cleanup
          docker system prune -f || true
          
          # Final system status
          echo "Final system status:"
          free -h
          df -h
          
          echo "✅ Cleanup completed"
