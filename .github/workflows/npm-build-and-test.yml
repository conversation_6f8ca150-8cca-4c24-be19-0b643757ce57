name: Run Tests and Build (npm)

on:
  workflow_call:

jobs:
  test-and-build:
    runs-on: [self-hosted, Linux]
    timeout-minutes: 30  # Prevent hanging builds
    env:
      NODE_OPTIONS: "--max-old-space-size=4096"  # Increase Node.js memory limit
      CI: true
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js cache
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm' # Enable npm caching

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit --no-fund # Faster than npm install
        timeout-minutes: 10

      - name: Run Tests (with coverage)
        run: npm run test:coverage
        timeout-minutes: 15

      - name: Build
        run: npm run build:ci
        timeout-minutes: 20
        env:
          NODE_OPTIONS: "--max-old-space-size=6144"  # Extra memory for build process
      
      - name: Check build output
        run: |
          echo "Build completed successfully"
          ls -la dist/
          du -sh dist/
          echo "Build size: $(du -sh dist/ | cut -f1)"

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: dist/
          retention-days: 7  # Reduce storage usage