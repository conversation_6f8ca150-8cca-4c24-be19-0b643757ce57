name: Complete CI Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  # Health check before starting builds
  health-check:
    uses: ./.github/workflows/build-health-monitor.yml
    with:
      timeout_minutes: 5

  # test_and_build:
  #   needs: health-check
  #   uses: ./.github/workflows/npm-build-and-test.yml

  sonar:
    # needs: test_and_build
    uses: ./.github/workflows/sonarqube-analysis.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  semgrep:
    # needs: test_and_build
    uses: ./.github/workflows/semgrep-scan.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      KUBERNETES_CLUSTER_ID: ${{ secrets.KUBERNETES_CLUSTER_ID }}
      KUBERNETES_CONTEXT: ${{ secrets.KUBERNETES_CONTEXT }}
      SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}

  docker:
    needs: semgrep
    uses: ./.github/workflows/docker-push.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}