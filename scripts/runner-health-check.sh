#!/bin/bash

# Runner Health Check Script
# This script monitors system resources and prevents runner communication loss

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MIN_MEMORY_MB=2048
MIN_DISK_GB=10
MAX_LOAD_AVERAGE=4.0

echo -e "${BLUE}🔍 ATS Frontend - Runner Health Check${NC}"
echo "=================================================="

# Function to check memory
check_memory() {
    echo -e "\n${BLUE}📊 Memory Check${NC}"
    
    # Get available memory in MB
    AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    USED_MEM=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    
    echo "Total Memory: ${TOTAL_MEM}MB"
    echo "Used Memory: ${USED_MEM}MB"
    echo "Available Memory: ${AVAILABLE_MEM}MB"
    
    if [ "$AVAILABLE_MEM" -lt "$MIN_MEMORY_MB" ]; then
        echo -e "${RED}❌ Warning: Low memory available (${AVAILABLE_MEM}MB < ${MIN_MEMORY_MB}MB)${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Memory check passed${NC}"
        return 0
    fi
}

# Function to check disk space
check_disk() {
    echo -e "\n${BLUE}💾 Disk Space Check${NC}"
    
    # Get available disk space in GB
    AVAILABLE_DISK=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    USED_DISK=$(df -BG . | awk 'NR==2{print $3}' | sed 's/G//')
    TOTAL_DISK=$(df -BG . | awk 'NR==2{print $2}' | sed 's/G//')
    
    echo "Total Disk: ${TOTAL_DISK}GB"
    echo "Used Disk: ${USED_DISK}GB"
    echo "Available Disk: ${AVAILABLE_DISK}GB"
    
    if [ "$AVAILABLE_DISK" -lt "$MIN_DISK_GB" ]; then
        echo -e "${RED}❌ Warning: Low disk space (${AVAILABLE_DISK}GB < ${MIN_DISK_GB}GB)${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Disk space check passed${NC}"
        return 0
    fi
}

# Function to check CPU load
check_cpu() {
    echo -e "\n${BLUE}⚡ CPU Load Check${NC}"
    
    # Get 1-minute load average
    LOAD_1MIN=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs)
    
    echo "1-minute load average: ${LOAD_1MIN}"
    
    # Compare load (bash doesn't support floating point comparison directly)
    if (( $(echo "$LOAD_1MIN > $MAX_LOAD_AVERAGE" | bc -l) )); then
        echo -e "${RED}❌ Warning: High CPU load (${LOAD_1MIN} > ${MAX_LOAD_AVERAGE})${NC}"
        return 1
    else
        echo -e "${GREEN}✅ CPU load check passed${NC}"
        return 0
    fi
}

# Function to check Docker
check_docker() {
    echo -e "\n${BLUE}🐳 Docker Check${NC}"
    
    if command -v docker &> /dev/null; then
        echo "Docker is available"
        
        # Check Docker system usage
        if docker system df &> /dev/null; then
            echo "Docker system usage:"
            docker system df
            
            # Get Docker space usage
            DOCKER_SIZE=$(docker system df | grep "Total" | awk '{print $4}' | sed 's/GB//' | sed 's/MB/0.001*/' | bc)
            
            if (( $(echo "$DOCKER_SIZE > 5" | bc -l) )); then
                echo -e "${YELLOW}⚠️ Docker using significant space (${DOCKER_SIZE}GB)${NC}"
            fi
        fi
        
        echo -e "${GREEN}✅ Docker check passed${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️ Docker not available${NC}"
        return 0
    fi
}

# Function to cleanup resources
cleanup_resources() {
    echo -e "\n${YELLOW}🧹 Attempting to free resources...${NC}"
    
    # Clear npm cache
    if command -v npm &> /dev/null; then
        echo "Clearing npm cache..."
        npm cache clean --force || true
    fi
    
    # Clear yarn cache
    if command -v yarn &> /dev/null; then
        echo "Clearing yarn cache..."
        yarn cache clean || true
    fi
    
    # Docker cleanup
    if command -v docker &> /dev/null; then
        echo "Cleaning Docker system..."
        docker system prune -f || true
    fi
    
    # Clear temporary files
    echo "Clearing temporary files..."
    rm -rf /tmp/npm-* || true
    rm -rf /tmp/yarn-* || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Function to generate report
generate_report() {
    echo -e "\n${BLUE}📋 System Report${NC}"
    echo "=================================================="
    echo "Timestamp: $(date)"
    echo "Hostname: $(hostname)"
    echo "Uptime: $(uptime -p)"
    echo ""
    
    echo "Memory:"
    free -h
    echo ""
    
    echo "Disk Usage:"
    df -h
    echo ""
    
    echo "Top Processes by Memory:"
    ps aux --sort=-%mem | head -10
    echo ""
    
    if command -v docker &> /dev/null; then
        echo "Docker Containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Size}}" || true
    fi
}

# Main execution
main() {
    local memory_ok=0
    local disk_ok=0
    local cpu_ok=0
    local docker_ok=0
    
    # Run all checks
    check_memory && memory_ok=1
    check_disk && disk_ok=1
    check_cpu && cpu_ok=1
    check_docker && docker_ok=1
    
    # Calculate overall health
    local total_checks=3  # memory, disk, cpu (docker is optional)
    local passed_checks=$((memory_ok + disk_ok + cpu_ok))
    
    echo -e "\n${BLUE}📊 Health Summary${NC}"
    echo "=================================================="
    echo "Checks passed: ${passed_checks}/${total_checks}"
    
    if [ "$passed_checks" -eq "$total_checks" ]; then
        echo -e "${GREEN}✅ System is healthy for CI/CD builds${NC}"
        generate_report
        exit 0
    else
        echo -e "${RED}❌ System health issues detected${NC}"
        
        # Attempt cleanup if requested
        if [ "$1" = "--cleanup" ]; then
            cleanup_resources
            echo -e "\n${YELLOW}🔄 Re-running health checks after cleanup...${NC}"
            main
        else
            echo -e "${YELLOW}💡 Run with --cleanup to attempt automatic fixes${NC}"
            generate_report
            exit 1
        fi
    fi
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "Usage: $0 [--cleanup] [--report]"
        echo ""
        echo "Options:"
        echo "  --cleanup    Attempt to free resources if health checks fail"
        echo "  --report     Generate detailed system report"
        echo "  --help       Show this help message"
        exit 0
        ;;
    --report)
        generate_report
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
