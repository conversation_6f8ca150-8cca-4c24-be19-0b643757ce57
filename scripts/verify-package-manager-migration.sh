#!/bin/bash

# Package Manager Standardization Verification Script
# This script verifies that npm is used consistently across all environments

set -e

echo "🔍 Package Manager Standardization Verification"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if npm is available and working
check_npm_version() {
    print_status "Checking npm version..."

    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi

    npm_version=$(npm --version)
    print_success "npm version: $npm_version"

    # Check if npm can run basic commands
    if npm config get registry &> /dev/null; then
        print_success "npm configuration is working"
    else
        print_error "npm configuration has issues"
        return 1
    fi
}

# Check package.json configuration
check_package_json() {
    print_status "Checking package.json configuration..."

    if grep -q '"packageManager": "npm@' package.json; then
        print_success "Package manager correctly set to npm"
    else
        print_error "Package manager not set correctly in package.json"
        return 1
    fi

    # Check if scripts use npm instead of yarn
    if grep -q '"sonar:mock": "npm run' package.json; then
        print_success "Scripts correctly use npm commands"
    else
        print_error "Some scripts still use yarn commands"
        return 1
    fi
}

# Check if package-lock.json exists and yarn.lock doesn't
check_lockfiles() {
    print_status "Checking lockfile configuration..."

    if [[ -f "package-lock.json" ]]; then
        print_success "package-lock.json exists"
    else
        print_error "package-lock.json not found"
        return 1
    fi

    if [[ -f "yarn.lock" ]]; then
        print_warning "yarn.lock exists and should be removed"
        print_status "Removing yarn.lock..."
        rm yarn.lock
        print_success "yarn.lock removed"
    else
        print_success "yarn.lock correctly absent"
    fi
}

# Check Dockerfile
check_dockerfile() {
    print_status "Checking Dockerfile configuration..."

    if grep -q "package-lock.json" Dockerfile; then
        print_success "Dockerfile copies package-lock.json"
    else
        print_error "Dockerfile doesn't copy package-lock.json"
        return 1
    fi

    if grep -q "npm ci" Dockerfile; then
        print_success "Dockerfile uses npm ci"
    else
        print_error "Dockerfile doesn't use npm ci"
        return 1
    fi

    if grep -q "npm cache clean" Dockerfile; then
        print_success "Dockerfile uses npm cache clean"
    else
        print_error "Dockerfile doesn't use npm cache clean"
        return 1
    fi

    if grep -q "corepack enable" Dockerfile; then
        print_warning "Dockerfile still contains corepack commands (should be removed)"
        return 1
    else
        print_success "Dockerfile correctly doesn't use corepack"
    fi
}

# Check CI workflows
check_ci_workflows() {
    print_status "Checking CI workflow configurations..."

    # Check npm-build-and-test.yml
    if grep -q "cache: 'npm'" .github/workflows/npm-build-and-test.yml; then
        print_success "npm-build-and-test.yml uses npm caching"
    else
        print_error "npm-build-and-test.yml doesn't use npm caching"
        return 1
    fi

    if grep -q "npm ci" .github/workflows/npm-build-and-test.yml; then
        print_success "npm-build-and-test.yml uses npm ci"
    else
        print_error "npm-build-and-test.yml doesn't use npm ci"
        return 1
    fi

    # Check sonarqube-analysis.yml
    if grep -q "npm ci" .github/workflows/sonarqube-analysis.yml; then
        print_success "sonarqube-analysis.yml uses npm ci"
    else
        print_error "sonarqube-analysis.yml doesn't use npm ci"
        return 1
    fi

    # Check for yarn references that should be removed
    if grep -q "yarn" .github/workflows/npm-build-and-test.yml; then
        print_warning "npm-build-and-test.yml still contains yarn references"
        return 1
    else
        print_success "npm-build-and-test.yml correctly doesn't reference yarn"
    fi
}

# Test npm commands
test_npm_commands() {
    print_status "Testing npm commands..."

    # Test npm ci
    print_status "Testing npm ci..."
    if npm ci --prefer-offline --no-audit --no-fund; then
        print_success "npm ci works correctly"
    else
        print_error "npm ci failed"
        return 1
    fi

    # Test build command
    print_status "Testing build command..."
    if npm run build:ci-fast; then
        print_success "npm build command works correctly"
    else
        print_error "npm build command failed"
        return 1
    fi

    # Test test command
    print_status "Testing test command..."
    if npm run test --passWithNoTests; then
        print_success "npm test command works correctly"
    else
        print_warning "npm test command had issues (may be expected)"
    fi
}

# Check memory configuration
check_memory_config() {
    print_status "Checking memory configuration..."
    
    if grep -q "NODE_OPTIONS='--max-old-space-size=6144'" package.json; then
        print_success "Memory configuration optimized for CI"
    else
        print_error "Memory configuration not optimized"
        return 1
    fi
}

# Main verification function
main() {
    echo
    print_status "Starting package manager standardization verification..."
    echo

    local errors=0

    # Run all checks
    check_npm_version || ((errors++))
    echo

    check_package_json || ((errors++))
    echo

    check_lockfiles || ((errors++))
    echo

    check_dockerfile || ((errors++))
    echo

    check_ci_workflows || ((errors++))
    echo

    check_memory_config || ((errors++))
    echo

    test_npm_commands || ((errors++))
    echo

    # Summary
    if [[ $errors -eq 0 ]]; then
        print_success "🎉 All verification checks passed!"
        print_success "Package manager standardization is complete and working correctly."
        echo
        print_status "Next steps:"
        echo "  1. Commit and push changes"
        echo "  2. Test CI pipeline"
        echo "  3. Monitor build performance"
    else
        print_error "❌ $errors verification check(s) failed."
        print_error "Please review and fix the issues above."
        exit 1
    fi
}

# Run main function
main "$@"
