#!/bin/bash

# Package Manager Migration Verification Script
# This script verifies that the migration from npm to yarn is complete and working

set -e

echo "🔍 Package Manager Migration Verification"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if yarn is available and correct version
check_yarn_version() {
    print_status "Checking Yarn version..."
    
    if ! command -v yarn &> /dev/null; then
        print_error "Yarn is not installed or not in PATH"
        print_status "Installing yarn via corepack..."
        corepack enable
        corepack prepare yarn@4.1.0 --activate
    fi
    
    yarn_version=$(yarn --version)
    expected_version="4.1.0"
    
    if [[ "$yarn_version" == "$expected_version" ]]; then
        print_success "Yarn version correct: $yarn_version"
    else
        print_warning "Yarn version mismatch. Expected: $expected_version, Got: $yarn_version"
        print_status "Setting correct yarn version..."
        corepack prepare yarn@4.1.0 --activate
    fi
}

# Check package.json configuration
check_package_json() {
    print_status "Checking package.json configuration..."
    
    if grep -q '"packageManager": "yarn@4.1.0"' package.json; then
        print_success "Package manager correctly set to yarn@4.1.0"
    else
        print_error "Package manager not set correctly in package.json"
        return 1
    fi
    
    # Check if scripts use yarn instead of npm
    if grep -q '"sonar:mock": "yarn run' package.json; then
        print_success "Scripts correctly use yarn commands"
    else
        print_error "Some scripts still use npm commands"
        return 1
    fi
}

# Check if yarn.lock exists and package-lock.json doesn't
check_lockfiles() {
    print_status "Checking lockfile configuration..."
    
    if [[ -f "yarn.lock" ]]; then
        print_success "yarn.lock exists"
    else
        print_error "yarn.lock not found"
        return 1
    fi
    
    if [[ -f "package-lock.json" ]]; then
        print_warning "package-lock.json exists and should be removed"
        print_status "Removing package-lock.json..."
        rm package-lock.json
        print_success "package-lock.json removed"
    else
        print_success "package-lock.json correctly absent"
    fi
}

# Check Dockerfile
check_dockerfile() {
    print_status "Checking Dockerfile configuration..."
    
    if grep -q "corepack enable" Dockerfile; then
        print_success "Dockerfile uses corepack to enable yarn"
    else
        print_error "Dockerfile doesn't enable corepack"
        return 1
    fi
    
    if grep -q "yarn install --frozen-lockfile" Dockerfile; then
        print_success "Dockerfile uses yarn install"
    else
        print_error "Dockerfile still uses npm commands"
        return 1
    fi
    
    if grep -q "yarn cache clean" Dockerfile; then
        print_success "Dockerfile uses yarn cache clean"
    else
        print_error "Dockerfile still uses npm cache clean"
        return 1
    fi
}

# Check CI workflows
check_ci_workflows() {
    print_status "Checking CI workflow configurations..."
    
    # Check npm-build-and-test.yml
    if grep -q "cache: 'yarn'" .github/workflows/npm-build-and-test.yml; then
        print_success "npm-build-and-test.yml uses yarn caching"
    else
        print_error "npm-build-and-test.yml doesn't use yarn caching"
        return 1
    fi
    
    if grep -q "yarn install --frozen-lockfile" .github/workflows/npm-build-and-test.yml; then
        print_success "npm-build-and-test.yml uses yarn install"
    else
        print_error "npm-build-and-test.yml still uses npm install"
        return 1
    fi
    
    # Check sonarqube-analysis.yml
    if grep -q "yarn install --frozen-lockfile" .github/workflows/sonarqube-analysis.yml; then
        print_success "sonarqube-analysis.yml uses yarn install"
    else
        print_error "sonarqube-analysis.yml still uses npm install"
        return 1
    fi
}

# Test yarn commands
test_yarn_commands() {
    print_status "Testing yarn commands..."
    
    # Test yarn install
    print_status "Testing yarn install..."
    if yarn install --frozen-lockfile --network-timeout 300000; then
        print_success "yarn install works correctly"
    else
        print_error "yarn install failed"
        return 1
    fi
    
    # Test build command
    print_status "Testing build command..."
    if yarn run build:ci-fast; then
        print_success "yarn build command works correctly"
    else
        print_error "yarn build command failed"
        return 1
    fi
    
    # Test test command
    print_status "Testing test command..."
    if yarn run test --passWithNoTests; then
        print_success "yarn test command works correctly"
    else
        print_warning "yarn test command had issues (may be expected)"
    fi
}

# Check memory configuration
check_memory_config() {
    print_status "Checking memory configuration..."
    
    if grep -q "NODE_OPTIONS='--max-old-space-size=6144'" package.json; then
        print_success "Memory configuration optimized for CI"
    else
        print_error "Memory configuration not optimized"
        return 1
    fi
}

# Main verification function
main() {
    echo
    print_status "Starting package manager migration verification..."
    echo
    
    local errors=0
    
    # Run all checks
    check_yarn_version || ((errors++))
    echo
    
    check_package_json || ((errors++))
    echo
    
    check_lockfiles || ((errors++))
    echo
    
    check_dockerfile || ((errors++))
    echo
    
    check_ci_workflows || ((errors++))
    echo
    
    check_memory_config || ((errors++))
    echo
    
    test_yarn_commands || ((errors++))
    echo
    
    # Summary
    if [[ $errors -eq 0 ]]; then
        print_success "🎉 All verification checks passed!"
        print_success "Package manager migration is complete and working correctly."
        echo
        print_status "Next steps:"
        echo "  1. Commit and push changes"
        echo "  2. Test CI pipeline"
        echo "  3. Monitor build performance"
    else
        print_error "❌ $errors verification check(s) failed."
        print_error "Please review and fix the issues above."
        exit 1
    fi
}

# Run main function
main "$@"
