#!/bin/bash

# Docker Buildx Installation Script for Self-Hosted Runners
# This script installs Docker Buildx if it's not already available

set -e

echo "🔧 Docker Buildx Installation Script"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    docker_version=$(docker --version)
    print_success "Docker is installed: $docker_version"
}

# Check if buildx is already available
check_buildx() {
    print_status "Checking if Docker Buildx is already available..."
    if docker buildx version &> /dev/null; then
        buildx_version=$(docker buildx version)
        print_success "Docker Buildx is already installed: $buildx_version"
        return 0
    else
        print_warning "Docker Buildx is not available"
        return 1
    fi
}

# Install Docker Buildx
install_buildx() {
    print_status "Installing Docker Buildx..."
    
    # Create cli-plugins directory if it doesn't exist
    mkdir -p ~/.docker/cli-plugins
    
    # Detect architecture
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            BUILDX_ARCH="linux-amd64"
            ;;
        aarch64|arm64)
            BUILDX_ARCH="linux-arm64"
            ;;
        armv7l)
            BUILDX_ARCH="linux-arm-v7"
            ;;
        *)
            print_error "Unsupported architecture: $ARCH"
            exit 1
            ;;
    esac
    
    print_status "Detected architecture: $ARCH -> $BUILDX_ARCH"
    
    # Get latest buildx version
    print_status "Fetching latest buildx version..."
    BUILDX_VERSION=$(curl -s https://api.github.com/repos/docker/buildx/releases/latest | grep '"tag_name"' | cut -d'"' -f4)
    
    if [ -z "$BUILDX_VERSION" ]; then
        print_warning "Could not fetch latest version, using fallback version v0.12.1"
        BUILDX_VERSION="v0.12.1"
    fi
    
    print_status "Installing buildx version: $BUILDX_VERSION"
    
    # Download buildx
    BUILDX_URL="https://github.com/docker/buildx/releases/download/${BUILDX_VERSION}/buildx-${BUILDX_VERSION}.${BUILDX_ARCH}"
    
    print_status "Downloading from: $BUILDX_URL"
    
    if curl -L "$BUILDX_URL" -o ~/.docker/cli-plugins/docker-buildx; then
        chmod +x ~/.docker/cli-plugins/docker-buildx
        print_success "Docker Buildx downloaded and installed successfully"
    else
        print_error "Failed to download Docker Buildx"
        exit 1
    fi
}

# Verify installation
verify_installation() {
    print_status "Verifying Docker Buildx installation..."
    
    if docker buildx version &> /dev/null; then
        buildx_version=$(docker buildx version)
        print_success "✅ Docker Buildx is working: $buildx_version"
        
        # List available builders
        print_status "Available builders:"
        docker buildx ls
        
        return 0
    else
        print_error "❌ Docker Buildx installation failed"
        return 1
    fi
}

# Setup default builder
setup_builder() {
    print_status "Setting up default buildx builder..."
    
    # Create and use a new builder instance
    if docker buildx create --name mybuilder --use &> /dev/null; then
        print_success "Created and set new builder: mybuilder"
    else
        print_warning "Could not create new builder, using default"
    fi
    
    # Bootstrap the builder
    print_status "Bootstrapping builder..."
    if docker buildx inspect --bootstrap &> /dev/null; then
        print_success "Builder bootstrapped successfully"
    else
        print_warning "Builder bootstrap failed, but buildx should still work"
    fi
}

# Main execution
main() {
    echo
    print_status "Starting Docker Buildx installation process..."
    echo
    
    # Check Docker installation
    check_docker
    echo
    
    # Check if buildx is already available
    if check_buildx; then
        echo
        print_success "🎉 Docker Buildx is already available! No installation needed."
        docker buildx ls
        exit 0
    fi
    
    echo
    
    # Install buildx
    install_buildx
    echo
    
    # Verify installation
    if verify_installation; then
        echo
        setup_builder
        echo
        print_success "🎉 Docker Buildx installation completed successfully!"
        print_status "You can now use 'docker buildx build' commands"
    else
        print_error "❌ Installation verification failed"
        exit 1
    fi
}

# Run main function
main "$@"
