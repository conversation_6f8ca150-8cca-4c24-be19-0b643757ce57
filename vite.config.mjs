// https://github.com/vitejs/vite/discussions/3448
import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import jsconfigPaths from 'vite-jsconfig-paths';

// ----------------------------------------------------------------------

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isBeta = mode === 'beta';
  const isProd = mode === 'production';

  return {
    plugins: [react(), jsconfigPaths()],
    // https://github.com/jpuri/react-draft-wysiwyg/issues/1317
    base: process.env.VITE_APP_BASE_NAME,
    define: {
      global: 'window'
    },
    resolve: {
      alias: [
        {
          find: /^~(.+)/,
          replacement: path.join(process.cwd(), 'node_modules/$1')
        },
        {
          find: /^src(.+)/,
          replacement: path.join(process.cwd(), 'src/$1')
        }
      ]
    },
    // Build optimizations for faster CI/CD builds
    build: {
      // Enable faster builds
      target: 'esnext',
      minify: 'esbuild', // 2-3x faster than terser
      sourcemap: false, // Disable for faster builds

      // Memory and performance optimizations
      reportCompressedSize: false, // Skip gzip size reporting for faster builds

      // Optimize chunk splitting for better caching and parallel processing
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            mui: ['@mui/material', '@mui/icons-material'],
            utils: ['lodash', 'axios', 'date-fns'],
            routing: ['react-router-dom'],
            forms: ['formik', 'yup', 'react-hook-form'],
            charts: ['@tanstack/react-table'],
          },
          // Optimize chunk naming for better caching
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
        // Reduce memory usage during build
        maxParallelFileOps: 2,
      },

      // Reduce bundle size warnings
      chunkSizeWarningLimit: 1000,

      // Optimize for CI environments
      ...(process.env.CI && {
        emptyOutDir: true,
        reportCompressedSize: false,
        minify: 'esbuild',
        // Reduce memory usage in CI
        rollupOptions: {
          ...this.rollupOptions,
          maxParallelFileOps: 1, // Reduce parallel operations in CI
          output: {
            ...this.rollupOptions?.output,
            // Smaller chunks in CI to reduce memory pressure
            manualChunks: (id) => {
              if (id.includes('node_modules')) {
                if (id.includes('react') || id.includes('react-dom')) {
                  return 'vendor-react';
                }
                if (id.includes('@mui')) {
                  return 'vendor-mui';
                }
                return 'vendor-other';
              }
            },
          },
        },
      }),
    },
    
    // Optimize dependencies for faster builds
    optimizeDeps: {
      include: ['react', 'react-dom', '@mui/material', 'axios', 'lodash'],
      exclude: ['@mui/icons-material'], // Exclude heavy icon library from pre-bundling
      // Force optimization of commonly used dependencies
      force: process.env.CI === 'true',
    },

    // Worker configuration for better performance
    worker: {
      format: 'es',
    },

    // Experimental features for better performance
    experimental: {
      renderBuiltUrl: (filename) => {
        return `/${filename}`;
      },
    },
    
    server: {
      // this ensures that the browser opens upon server start
      open: true,
      // this sets a default port to 3000
      port: parseInt(env.VITE_APP_PORT || '3000'),
      proxy: {
        '/api': {
          target: env.VITE_APP_API_URL,
          changeOrigin: true,
          secure: false
        }
      }
    },
    preview: {
      // this ensures that the browser opens upon preview start
      open: true,
      // this sets a default port to 3000
      port: parseInt(env.VITE_APP_PORT || '3000')
    }
  };
});
