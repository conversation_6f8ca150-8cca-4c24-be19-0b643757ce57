# CI/CD Pipeline Troubleshooting Guide

This guide addresses common CI/CD pipeline issues and their solutions for the ATS Frontend Application.

## 🚨 Issue 1: Self-hosted Runner Communication Loss

### Problem Description
- Build process starts successfully with `vite build --mode production`
- Transforms 18,386 modules without errors
- Warning about eval usage in bluebird library appears
- Self-hosted runner loses communication with GitHub Actions server

### Root Causes
1. **Memory Exhaustion**: Large dependency tree (18,386 modules) overwhelming runner memory
2. **Build Timeout**: Long-running build process exceeding runner timeout limits
3. **Resource Contention**: Multiple concurrent builds competing for system resources
4. **Network Issues**: Intermittent connectivity problems with GitHub Actions

### Solutions Implemented

#### 1. Memory Management
```yaml
# .github/workflows/npm-build-and-test.yml
env:
  NODE_OPTIONS: "--max-old-space-size=6144"  # Increased Node.js memory limit
```

#### 2. Build Timeouts
```yaml
timeout-minutes: 30  # Prevent hanging builds
steps:
  - name: Install dependencies
    timeout-minutes: 10
  - name: Build
    timeout-minutes: 20
```

#### 3. Vite Configuration Optimizations
```javascript
// vite.config.mjs
build: {
  reportCompressedSize: false,  // Skip gzip reporting for faster builds
  maxParallelFileOps: 2,        // Reduce memory usage
  minify: 'esbuild',           // 2-3x faster than terser
}
```

#### 4. Enhanced Chunk Splitting
```javascript
manualChunks: {
  vendor: ['react', 'react-dom'],
  mui: ['@mui/material', '@mui/icons-material'],
  utils: ['lodash', 'axios', 'date-fns'],
  routing: ['react-router-dom'],
  forms: ['formik', 'yup', 'react-hook-form'],
}
```

### Monitoring and Prevention

#### Health Check Workflow
- Pre-build system resource verification
- Real-time memory and CPU monitoring during builds
- Automatic cleanup of caches and temporary files

#### Build Optimization Scripts
```bash
# Fast CI build with reduced logging
npm run build:ci-fast

# Standard CI build with memory optimization
npm run build:ci
```

## 🐳 Issue 2: Docker Build Failure - Invalid Reference Format

### Problem Description
```bash
docker build -t ATS-Frontend-Application .
# Error: invalid reference format (uppercase letters not allowed)
```

### Root Cause
Docker image names must follow specific naming conventions:
- Only lowercase letters, numbers, hyphens, and underscores
- No uppercase letters allowed

### Solution Implemented
```yaml
# .github/workflows/docker-push.yml
env:
  APP_NAME: ats-frontend-application  # Changed from ATS-Frontend-Application
```

### Additional Docker Optimizations
```yaml
env:
  DOCKER_BUILDKIT: 1  # Enable BuildKit for faster builds

steps:
  - name: Build Universal Docker Image
    run: |
      docker system prune -f || true  # Clean up before build
      docker build --no-cache --progress=plain -t ${{ env.APP_NAME }} .
    timeout-minutes: 30
```

## 🔧 General Troubleshooting Steps

### 1. Check Runner Resources
```bash
# Memory usage
free -h

# Disk space
df -h

# CPU load
uptime

# Docker system usage
docker system df
```

### 2. Clean Up Commands
```bash
# Clear npm cache
npm cache clean --force

# Clear yarn cache
yarn cache clean

# Docker cleanup
docker system prune -f

# Remove node_modules cache
rm -rf node_modules/.cache
```

### 3. Build Debugging
```bash
# Run build with verbose logging
NODE_OPTIONS="--max-old-space-size=6144" npm run build:ci

# Check build output size
du -sh dist/

# Analyze bundle size
npx vite-bundle-analyzer dist
```

## 📊 Performance Monitoring

### Key Metrics to Monitor
1. **Build Time**: Should be under 20 minutes
2. **Memory Usage**: Peak usage should not exceed 6GB
3. **Disk Space**: Ensure at least 10GB free space
4. **Network Connectivity**: Monitor GitHub Actions connectivity

### Alert Thresholds
- Build time > 25 minutes: Investigate resource constraints
- Memory usage > 80%: Consider increasing runner resources
- Disk usage > 90%: Clean up old builds and caches

## 🚀 Best Practices

### 1. Resource Management
- Use `NODE_OPTIONS` to set appropriate memory limits
- Implement build timeouts to prevent hanging processes
- Regular cleanup of caches and temporary files

### 2. Build Optimization
- Use esbuild for faster minification
- Disable source maps in CI builds
- Implement efficient chunk splitting strategy

### 3. Docker Best Practices
- Use alpine images for smaller size
- Multi-stage builds to reduce final image size
- Proper cleanup in Dockerfile

### 4. Monitoring
- Implement health checks before builds
- Monitor system resources during builds
- Set up alerts for build failures

## 🔄 Recovery Procedures

### If Runner Loses Communication
1. Check runner status in GitHub repository settings
2. Restart the self-hosted runner service
3. Clear runner cache and temporary files
4. Re-run failed workflow

### If Build Fails Due to Memory
1. Increase `NODE_OPTIONS` memory limit
2. Clear all caches before retry
3. Consider splitting build into smaller chunks
4. Monitor system resources during retry

### If Docker Build Fails
1. Verify image name follows naming conventions
2. Clean Docker system before retry
3. Check available disk space
4. Review Dockerfile for optimization opportunities
