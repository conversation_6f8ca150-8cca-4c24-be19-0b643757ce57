# ATS Frontend Application - CI/CD Implementation

This document describes the complete CI/CD pipeline implementation for the ATS Frontend Application, based on the ai-react-frontend patterns.

## 🚀 Overview

The CI/CD pipeline implements a comprehensive workflow that includes:
- **Testing & Building**: Automated testing with coverage and production builds
- **Code Quality**: SonarQube analysis with quality gates
- **Security Scanning**: Semgrep static analysis in Kubernetes
- **Containerization**: Docker image building and pushing to DOCR
- **GitOps Deployment**: Automated deployment via ArgoCD

## 📋 Prerequisites

### Required GitHub Secrets

Set up the following secrets in your GitHub repository:

1. **SONAR_TOKEN**: SonarQube authentication token
   - Get from: http://***************:9000
   - Used for: SonarQube analysis

2. **DIGITALOCEAN_ACCESS_TOKEN**: DigitalOcean API token
   - Get from: DigitalOcean Console → API → Generate New Token
   - Used for: Docker registry authentication and Kubernetes access

3. **KUBERNETES_CLUSTER_ID**: DOKS cluster ID
   - Get from: `doctl kubernetes cluster list`
   - Used for: Semgrep scanning in Kubernetes

4. **KUBERNETES_CONTEXT**: Kubernetes context name
   - Get from: `kubectl config get-contexts`
   - Used for: Semgrep scanning in Kubernetes

5. **SECURE_GITHUB_TOKEN**: GitHub token for Semgrep
   - Create at: GitHub → Settings → Developer settings → Personal access tokens
   - Used for: Cloning repository in Semgrep job

6. **GITOPS_TOKEN**: Token for GitOps repository
   - Create at: GitHub → Settings → Developer settings → Personal access tokens
   - Used for: Triggering ArgoCD deployments

### Environment Files

Ensure the following environment files exist:
- `.env.development` - Development environment variables
- `.env.beta` - Beta environment variables  
- `.env.production` - Production environment variables

## 🔄 Pipeline Flow

### CI Pipeline (ci.yml)
Triggered on: `push` to `main` and `pull_request` to `main`

1. **Test & Build** (`npm-build-and-test.yml`)
   - Runs on: Self-hosted Linux runner
   - Actions: Install dependencies, run tests with coverage, build production app
   - Output: Build artifacts uploaded

2. **SonarQube Analysis** (`sonarqube-analysis.yml`)
   - Runs on: Self-hosted Linux X64 runner
   - Actions: Build app, run tests, perform SonarQube analysis
   - Quality Gate: Waits for SonarQube quality gate results

3. **Semgrep Security Scan** (`semgrep-scan.yml`)
   - Runs on: Self-hosted Linux runner
   - Actions: Deploy Semgrep job to DOKS, scan code, analyze results
   - Security: Fails pipeline if blocking issues found

### CD Pipeline (cd.yml)
Triggered on: `push` to `main`

1. **Test & Build** (same as CI)
2. **SonarQube Analysis** (same as CI)
3. **Semgrep Security Scan** (same as CI)
4. **Docker Build & Push** (`docker-push.yml`)
   - Actions: Build universal Docker image, tag, push to DOCR
   - Image: `registry.digitalocean.com/doks-registry/ats-frontend-application:latest`

5. **GitOps Deployment** (`deploy-gitops.yml`)
   - Actions: Trigger ArgoCD deployment via repository dispatch
   - Target: `ChidhagniConsulting/gitops-argocd-apps`

## 🏗️ Architecture

### Universal Runtime Configuration
The Docker image supports backend switching via ConfigMap:
- **Build-time**: Uses `.env.production` for build variables
- **Runtime**: Backend URLs configured via Kubernetes ConfigMap
- **Compatibility**: Supports Spring, Django, Nest backends

### Kubernetes Integration
- **Semgrep Job**: Runs in DOKS cluster for security scanning
- **Results Reader**: Pod that reads scan results from PVC
- **Namespace**: `semgrep` namespace for isolation

### Quality Gates
- **SonarQube**: 80% minimum code coverage
- **Semgrep**: No WARNING or ERROR severity issues
- **Tests**: All unit and integration tests must pass

## 📁 File Structure

```
ATS-Frontend-Application/
├── .github/
│   ├── workflows/
│   │   ├── ci.yml                    # Main CI pipeline
│   │   ├── cd.yml                    # Main CD pipeline
│   │   ├── npm-build-and-test.yml   # Test and build workflow
│   │   ├── sonarqube-analysis.yml   # SonarQube analysis
│   │   ├── semgrep-scan.yml         # Security scanning
│   │   ├── docker-push.yml          # Docker build and push
│   │   └── deploy-gitops.yml        # GitOps deployment
│   ├── PULL_REQUEST_TEMPLATE.md     # PR template
│   └── CODEOWNERS                   # Code ownership
├── k8s/
│   ├── semgrep-job.yaml             # Semgrep Kubernetes job
│   └── semgrep-results-reader.yaml  # Results reader pod
├── .semgrepignore                   # Semgrep ignore patterns
├── .semgrep.yml                     # Semgrep configuration
├── sonar-project.properties         # SonarQube configuration
└── Dockerfile                       # Universal Docker build
```

## 🔧 Configuration

### SonarQube Configuration
- **Project Key**: `ats-frontend-application`
- **Host**: `http://***************:9000`
- **Coverage**: 80% minimum
- **Sources**: `src/**/*.js,src/**/*.jsx`
- **Tests**: `test/unit/**/*.test.js,test/unit/**/*.test.jsx`

### Semgrep Configuration
- **Config**: `auto` (uses Semgrep's auto-configuration)
- **Output**: JSON format
- **Severity**: Fails on WARNING and ERROR
- **Ignore**: Patterns in `.semgrepignore`

### Docker Configuration
- **Base Image**: `node:18` for build, `nginx:stable-alpine` for runtime
- **Registry**: `registry.digitalocean.com/doks-registry`
- **Image**: `ats-frontend-application:latest`
- **Build Args**: `ENV_FILE`, `BUILD_SCRIPT`
- **Build Strategy**: Adaptive (BuildKit if available, traditional otherwise)
- **BuildKit**: Auto-detected, falls back to traditional Docker build

## 🚀 Deployment

### Environments
- **Development**: Feature branches → `dev` environment
- **Staging**: `staging` branch → `staging` environment  
- **Production**: `main` branch → `production` environment

### GitOps Flow
1. Code pushed to `main` branch
2. CI/CD pipeline runs all checks
3. Docker image built and pushed to DOCR
4. GitOps deployment triggered via repository dispatch
5. ArgoCD deploys to target environment

## 📊 Monitoring

### Pipeline Status
- Monitor CI/CD runs: GitHub Actions tab
- View SonarQube results: http://***************:9000
- Check ArgoCD deployment: GitOps repository

### Artifacts
- **Build Artifacts**: Available in GitHub Actions
- **Semgrep Results**: Uploaded as workflow artifacts
- **Docker Images**: Available in DOCR registry

## 🔍 Troubleshooting

### Common Issues

1. **SonarQube Analysis Fails**
   - Check SONAR_TOKEN secret
   - Verify SonarQube server is accessible
   - Check coverage requirements

2. **Semgrep Scan Fails**
   - Verify Kubernetes cluster access
   - Check SECURE_GITHUB_TOKEN permissions
   - Review .semgrepignore patterns

3. **Docker Build Fails**
   - Check DIGITALOCEAN_ACCESS_TOKEN
   - Verify DOCR registry access
   - Check Dockerfile syntax

4. **Docker BuildKit Error**
   ```
   ERROR: BuildKit is enabled but the buildx component is missing or broken.
   ```
   - **Solution**: Workflow now auto-detects buildx and falls back to traditional build
   - **Manual Fix**: Run `npm run install-buildx` on self-hosted runners
   - **See**: [Docker BuildKit Troubleshooting Guide](./docker-buildkit-troubleshooting.md)

5. **GitOps Deployment Fails**
   - Verify GITOPS_TOKEN permissions
   - Check ArgoCD application exists
   - Review deployment payload

### Debug Commands

```bash
# Check Kubernetes access
kubectl get pods -n semgrep

# View Semgrep results
kubectl cp semgrep/semgrep-results-reader:/results/results.json ./results.json

# Test SonarQube connection
sonar-scanner -Dsonar.host.url=http://***************:9000 -Dsonar.login=YOUR_TOKEN

# Check Docker registry
doctl registry login
docker pull registry.digitalocean.com/doks-registry/ats-frontend-application:latest
```

## 📝 Maintenance

### Regular Tasks
- Update Semgrep rules and ignore patterns
- Review SonarQube quality gate thresholds
- Monitor pipeline performance and resource usage
- Update Docker base images and dependencies

### Security Updates
- Regularly update Semgrep to latest version
- Review and update security scanning rules
- Monitor for new security vulnerabilities

## 🤝 Contributing

When contributing to the CI/CD pipeline:

1. Follow the existing workflow patterns
2. Test changes in a feature branch first
3. Update documentation for any new configurations
4. Ensure all quality gates still pass
5. Update this README if needed

## 📞 Support

For issues with the CI/CD pipeline:
1. Check the troubleshooting section above
2. Review GitHub Actions logs for detailed error messages
3. Contact the DevOps team for complex issues
4. Create an issue in the repository for persistent problems 