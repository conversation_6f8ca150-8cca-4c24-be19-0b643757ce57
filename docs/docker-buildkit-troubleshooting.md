# Docker BuildKit Troubleshooting Guide

## 🚨 Issue: BuildKit Component Missing

### Error Message
```
ERROR: BuildKit is enabled but the buildx component is missing or broken.
       Install the buildx component to build images with BuildKit:
       https://docs.docker.com/go/buildx/
Error: Process completed with exit code 1.
```

### Root Cause
The CI/CD pipeline was configured with `DOCKER_BUILDKIT: 1` but the self-hosted runner doesn't have Docker Buildx installed or properly configured.

## 🔧 Solutions Implemented

### Solution 1: Adaptive Build Strategy (Recommended)
The workflow now automatically detects buildx availability and uses the appropriate build method:

```yaml
- name: Setup Docker Buildx (Optional)
  id: buildx
  run: |
    if docker buildx version >/dev/null 2>&1; then
      echo "✅ Docker Buildx is available"
      echo "BUILDX_AVAILABLE=true" >> $GITHUB_ENV
      echo "DOCKER_BUILDKIT=1" >> $GITHUB_ENV
    else
      echo "⚠️  Docker Buildx not available, using traditional Docker build"
      echo "BUILDX_AVAILABLE=false" >> $GITHUB_ENV
      echo "DOCKER_BUILDKIT=0" >> $GITHUB_ENV
    fi
```

### Solution 2: Conditional Build Commands
```yaml
- name: Build Universal Docker Image
  run: |
    if [ "${{ env.BUILDX_AVAILABLE }}" = "true" ]; then
      docker buildx build --load -t ${{ env.APP_NAME }} .
    else
      docker build -t ${{ env.APP_NAME }} .
    fi
```

## 🛠️ Manual Solutions for Self-Hosted Runners

### Option A: Install Docker Buildx
```bash
# Download and install buildx
mkdir -p ~/.docker/cli-plugins
curl -L https://github.com/docker/buildx/releases/latest/download/buildx-v0.12.1.linux-amd64 \
  -o ~/.docker/cli-plugins/docker-buildx
chmod +x ~/.docker/cli-plugins/docker-buildx

# Verify installation
docker buildx version
```

### Option B: Use Docker Desktop
Docker Desktop includes buildx by default. Install Docker Desktop on the runner machine.

### Option C: Disable BuildKit Globally
```bash
# Add to runner's environment
echo 'export DOCKER_BUILDKIT=0' >> ~/.bashrc
source ~/.bashrc
```

## 🔍 Verification Steps

### Check BuildKit Status
```bash
# Check if buildx is available
docker buildx version

# Check BuildKit status
docker version --format '{{.Server.Experimental}}'

# List available builders
docker buildx ls
```

### Test Build Commands
```bash
# Test traditional build
docker build -t test-image .

# Test buildx build (if available)
docker buildx build -t test-image .
```

## 📊 Performance Comparison

### Traditional Docker Build
- ✅ Works on all Docker installations
- ✅ No additional setup required
- ⚠️ Slower build times
- ⚠️ Limited caching options

### Docker Buildx
- ✅ Faster builds with advanced caching
- ✅ Multi-platform builds
- ✅ Better progress output
- ❌ Requires additional setup
- ❌ May not be available on older systems

## 🚀 Recommended Approach

1. **Use the adaptive strategy** implemented in the workflow
2. **Install buildx on runners** for better performance when possible
3. **Monitor build times** and optimize accordingly
4. **Keep fallback to traditional builds** for compatibility

## 🔄 Workflow Changes Made

### Before (Problematic)
```yaml
env:
  DOCKER_BUILDKIT: 1  # Always enabled

steps:
  - name: Build Docker Image
    run: docker build -t app .  # Fails if buildx missing
```

### After (Fixed)
```yaml
env:
  # DOCKER_BUILDKIT: 1  # Disabled by default

steps:
  - name: Setup Docker Buildx
    run: |
      if docker buildx version; then
        echo "BUILDX_AVAILABLE=true" >> $GITHUB_ENV
      else
        echo "BUILDX_AVAILABLE=false" >> $GITHUB_ENV
      fi
  
  - name: Build Docker Image
    run: |
      if [ "$BUILDX_AVAILABLE" = "true" ]; then
        docker buildx build --load -t app .
      else
        docker build -t app .
      fi
```

## 🐛 Troubleshooting

### If buildx installation fails:
```bash
# Check Docker version
docker --version

# Update Docker to latest version
sudo apt-get update && sudo apt-get install docker-ce docker-ce-cli

# Restart Docker service
sudo systemctl restart docker
```

### If builds still fail:
1. Check available disk space: `df -h`
2. Clean Docker system: `docker system prune -f`
3. Restart Docker daemon: `sudo systemctl restart docker`
4. Check runner logs for detailed error messages

## 📝 Best Practices

1. **Always provide fallbacks** for different Docker configurations
2. **Test on multiple environments** before deploying
3. **Monitor build performance** and adjust strategy accordingly
4. **Keep documentation updated** with current solutions
5. **Use health checks** to verify runner capabilities before builds

## 🔗 References

- [Docker Buildx Documentation](https://docs.docker.com/buildx/)
- [Docker BuildKit Documentation](https://docs.docker.com/develop/dev-best-practices/)
- [GitHub Actions Self-Hosted Runners](https://docs.github.com/en/actions/hosting-your-own-runners)
