# Package Manager Standardization Guide: Consistent npm Usage

## 🚨 Critical Issues Resolved

This guide documents the resolution of three critical CI/CD pipeline issues by standardizing on npm as the package manager across all environments.

### Issues Fixed:
1. **Docker Build Failure**: Package manager mismatch and missing lockfiles
2. **Runner Communication Loss**: Memory exhaustion during builds
3. **SonarQube Analysis Failure**: Dependency installation failures

## 🔧 Root Cause Analysis

### Issue 1: Package Manager Inconsistency
**Problem**: Mixed usage of npm and yarn across different environments
**Impact**: `npm ci` failed when `yarn.lock` existed but no `package-lock.json` was present

### Issue 2: Memory Exhaustion
**Problem**: Insufficient memory configuration and inconsistent package manager usage
**Impact**: Self-hosted runner lost communication during build process

### Issue 3: SonarQube Integration
**Problem**: Inconsistent package manager usage in different workflows
**Impact**: Dependency installation failed, preventing code analysis

## ✅ Solutions Implemented

### 1. Dockerfile Standardization to npm

**Before (Problematic - Mixed package managers):**
```dockerfile
# Inconsistent: copying yarn.lock but using npm commands
COPY package.json yarn.lock ./
RUN npm ci --only=production

# Build application
RUN npm run $BUILD_SCRIPT

# Clean up
RUN npm cache clean --force
```

**After (Fixed - Consistent npm usage):**
```dockerfile
# Copy npm lockfile
COPY package.json package-lock.json ./

# Install dependencies with optimizations using npm
RUN npm ci --only=production --no-audit --no-fund --prefer-offline

# Build application
RUN npm run $BUILD_SCRIPT

# Clean up
RUN npm cache clean --force
```

### 2. CI Workflow Standardization

**Before (Inconsistent - Mixed cache and commands):**
```yaml
- name: Setup Node.js cache
  uses: actions/cache@v3
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

- name: Set up Node.js
  with:
    cache: 'npm'

- name: Install dependencies
  run: npm ci --prefer-offline  # Failed due to missing package-lock.json
```

**After (Fixed - Consistent npm usage):**
```yaml
- name: Setup Node.js cache
  uses: actions/cache@v3
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

- name: Set up Node.js
  with:
    cache: 'npm'

- name: Install dependencies
  run: npm ci --prefer-offline --no-audit --no-fund
```

### 3. SonarQube Workflow Standardization

**Changes Made:**
- Ensured consistent npm cache usage
- Standardized dependency installation to use npm ci
- Fixed SonarScanner installation to use npm
- Updated all test execution to use npm run

### 4. Enhanced Memory Configuration

**Package.json Scripts:**
```json
{
  "build:ci": "NODE_OPTIONS='--max-old-space-size=6144' vite build --mode production --logLevel warn",
  "build:ci-fast": "NODE_OPTIONS='--max-old-space-size=4096' vite build --mode production --logLevel error"
}
```

**Vite Configuration:**
```javascript
// Optimize for CI environments
...(process.env.CI && {
  emptyOutDir: true,
  reportCompressedSize: false,
  minify: 'esbuild',
  rollupOptions: {
    maxParallelFileOps: 1, // Reduce parallel operations in CI
    output: {
      manualChunks: (id) => {
        // Smaller chunks to reduce memory pressure
        if (id.includes('node_modules')) {
          if (id.includes('react')) return 'vendor-react';
          if (id.includes('@mui')) return 'vendor-mui';
          return 'vendor-other';
        }
      },
    },
  },
}),
```

## 🚀 Performance Improvements

### Before Migration:
- ❌ Docker builds failed with package manager errors
- ❌ CI builds caused runner communication loss
- ❌ SonarQube analysis never executed
- ❌ Inconsistent dependency resolution

### After Standardization:
- ✅ Docker builds complete successfully
- ✅ CI builds stable with proper memory management
- ✅ SonarQube analysis runs correctly
- ✅ Consistent npm usage across all environments
- ✅ Reliable dependency installation with npm ci
- ✅ Proper lockfile management with package-lock.json

## 📊 Key Benefits

### 1. Consistency
- **Single Package Manager**: npm across all environments
- **Unified Lockfile**: Only `package-lock.json` used everywhere
- **Consistent Commands**: All scripts use `npm run`

### 2. Performance
- **Fast Installs**: npm ci for production builds
- **Efficient Caching**: npm cache with GitHub Actions
- **Memory Optimization**: Reduced memory usage in CI

### 3. Reliability
- **Deterministic Builds**: `npm ci` ensures exact dependency versions
- **Network Resilience**: `--prefer-offline` and `--no-audit` flags
- **Error Recovery**: Robust error handling and timeouts

## 🔍 Verification Steps

### 1. Local Development
```bash
# Verify npm version
npm --version

# Install dependencies
npm install

# Run tests
npm test

# Build application
npm run build:ci
```

### 2. Docker Build
```bash
# Build Docker image
docker build -t ats-frontend .

# Verify build success
docker run --rm ats-frontend nginx -t
```

### 3. CI Pipeline
- Check GitHub Actions for successful builds
- Verify SonarQube analysis completion
- Monitor memory usage during builds

## 🐛 Troubleshooting

### Common Issues

#### 1. Package Lock Issues
```bash
# Fix: Regenerate package-lock.json
rm package-lock.json
npm install
```

#### 2. Cache Issues
```bash
# Clear npm cache
npm cache clean --force

# Clear CI cache (in GitHub Actions)
# Delete cache keys containing 'node' in repository settings
```

#### 3. Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=6144"

# Use fast build for testing
npm run build:ci-fast
```

#### 4. Network Issues
```bash
# Use offline mode when possible
npm ci --prefer-offline --no-audit --no-fund
```

## 📝 Standardization Checklist

- [x] Update Dockerfile to use npm consistently
- [x] Migrate npm-build-and-test.yml to use npm ci
- [x] Migrate sonarqube-analysis.yml to use npm
- [x] Update package.json scripts to use npm
- [x] Generate package-lock.json file
- [x] Remove yarn.lock file
- [x] Update packageManager field in package.json
- [x] Optimize Vite configuration for CI
- [x] Update documentation
- [x] Test Docker builds locally
- [x] Verify CI pipeline execution
- [x] Confirm SonarQube analysis

## 🔗 Related Files Modified

1. `Dockerfile` - Package manager migration
2. `.github/workflows/npm-build-and-test.yml` - CI workflow
3. `.github/workflows/sonarqube-analysis.yml` - SonarQube workflow
4. `package.json` - Scripts and commands
5. `vite.config.mjs` - Build optimization
6. `docs/package-manager-migration-guide.md` - This documentation

## 🎯 Next Steps

1. **Monitor Performance**: Track build times and memory usage
2. **Update Documentation**: Keep standardization guide current
3. **Team Training**: Ensure team uses npm commands consistently
4. **Dependency Updates**: Use npm for all package management

---

**Status**: ✅ **STANDARDIZATION COMPLETE**
**Risk Level**: 🟢 **LOW** (Thoroughly tested)
**Rollback**: Available via Git history if needed
