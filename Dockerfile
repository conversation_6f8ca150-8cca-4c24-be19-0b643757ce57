# Step 1: Use Node.js to build the app
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Set Node.js memory limit for build process
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Copy dependency files
COPY package.json package-lock.json ./

# Install dependencies with optimizations using npm
RUN npm ci --only=production --no-audit --no-fund --prefer-offline
 
# Copy the rest of the source code
COPY . .

# Build the production-ready app with runtime configuration
ARG ENV_FILE=.env.production
ARG BUILD_SCRIPT=build:prod

# Increase memory limit for build and build the application
ENV NODE_OPTIONS="--max-old-space-size=6144"
RUN npm run $BUILD_SCRIPT

# Clean up to reduce image size
RUN rm -rf node_modules/.cache \
    && rm -rf src \
    && rm -rf public \
    && npm cache clean --force
 
# Step 2: Serve the app using Nginx 
FROM nginx:stable-alpine 
 
# Copy build output to Nginx web root 
COPY --from=build /app/dist /usr/share/nginx/html 
 
# Replace default Nginx config 
COPY nginx.conf /etc/nginx/conf.d/default.conf 
 
# Expose port 80 
EXPOSE 80 
 
# Start Nginx 
CMD ["nginx", "-g", "daemon off;"] 
