# SonarQube Configuration for ATS Frontend Application
sonar.projectKey=ats-frontend-application
sonar.projectName=ATS Frontend Application
sonar.projectVersion=9.2.0

# Include source files for analysis
sonar.sources=src
sonar.inclusions=src/**/*.js,src/**/*.jsx

# Exclude test files and other non-source files
sonar.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*,**/*.d.ts,**/src/index.jsx,**/src/config.js,**/src/setupTests.js,**/src/reportWebVitals.js,**/src/vite-env.d.js

# Test configuration
sonar.tests=test
sonar.test.inclusions=test/unit/**/*.test.js,test/unit/**/*.test.jsx,test/integration/**/*.test.js,test/integration/**/*.test.jsx

# Coverage configuration
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*

# Set minimum coverage to 80%
sonar.coverage.minimum=80

# Language
sonar.language=js

# Encoding
sonar.sourceEncoding=UTF-8

# Quality Gate
sonar.qualitygate.wait=true

# Node.js Memory Configuration - Fix for memory issues
sonar.javascript.node.maxspace=4096
sonar.javascript.node.debugMemory=true

# Additional settings
sonar.host.url=http://***************:9000
# sonar.token=sqp_f182504535bb863594d1fe3057b373f72a4664d2